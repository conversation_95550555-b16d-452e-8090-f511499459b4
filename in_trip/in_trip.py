import json
from datetime import datetime, timedelta, timezone
from typing import Any
from zoneinfo import ZoneInfo

import dateutil.parser
from langchain_core.messages import AIMessage
from pydantic import BaseModel

from agent.agent import Agent
from baml_client import b
from baml_client.types import FlightChangeEvent, HotelChangeEvent
from flight_agent.flight_data import FlightInfo
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User
from server.schemas.partners.spotnana.travel_delivery import SpotnanaTravelDelivery
from server.services.memory.trips.retriever import TripMemoryRetriever
from server.services.notifications.push_app_notifications import send_event_notification
from server.utils.logger import logger
from server.utils.settings import settings
from server.utils.websocket_no_op import partial_no_op
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import Timings


class InTripFlightInfo(FlightInfo):
    leg_index: int
    confirmation_number: str
    boarding_time: str | None = None
    gate: str | None = None
    terminal: str | None = None


class InTripHotelInfo(BaseModel):
    hotel_name: str
    check_in_date: str
    check_in_time: str
    check_out_date: str
    check_out_time: str
    room_type: str
    address: str
    confirmation_number: str


class InTripAgent(Agent):
    def __init__(self, user: User, thread: ChatThread):
        self.user = user
        self.thread = thread
        super().__init__(
            user=user,
            history=PostgresChatMessageHistory(thread_id=thread.id),
            mem_loader=TripMemoryRetriever(user_id=str(user.id), thread_id=str(thread.id)),
            websocket_send_message=partial_no_op,
        )

    async def handle_flight_changes_for_in_trip(
        self, data: SpotnanaTravelDelivery, booking: Booking, thread: ChatThread, user: User
    ):
        payload: dict = data.payload

        try:
            is_booking_in_trip = await InTripAgent.is_in_trip_booking(booking)
            if not is_booking_in_trip:
                logger.info(f"[IN_TRIP] Flight booking {booking.id} is not in trip, skipping.")
                return

            extracted_data: dict[str, Any] = await InTripAgent.extract_event_details(payload)

            t = Timings("BAML: ProcessFlightChangeEvent")
            results: FlightChangeEvent = await b.ProcessFlightChangeEvent(
                existing_booking=json.dumps(booking.content),
                change_event_details=json.dumps(extracted_data),
                current_date=get_current_date_string(),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

            logger.info(f"[IN_TRIP] Sending notification to user: {user.email}.")

            notificatioin_event = {
                "summary": results.change_summary,
                "description": results.agent_response,
            }
            await send_event_notification(notificatioin_event, user.email)

            msg = AIMessage(content=results.agent_response)
            msg.additional_kwargs = {
                "agent_classification": "InTrip",
                "is_in_trip_openning": True,
            }
            await self.persist_messages([msg])

        except Exception as e:
            logger.error(f"[IN_TRIP] Error handling flight changes: {str(e)}")

    async def handle_hotel_change_for_in_trip(self, hotel_booking: Booking, change: dict[str, Any], user: User):
        """Handle hotel changes from booking.com for in-trip bookings."""
        try:
            status = change.get("status", "")
            is_booking_in_trip = await InTripAgent.is_in_trip_booking(hotel_booking)
            if not is_booking_in_trip:
                logger.info(f"[IN_TRIP] Hotel booking {hotel_booking.id} is not in trip, skipping.")
                return

            t = Timings("BAML: ProcessHotelChangeEvent")
            results: HotelChangeEvent = await b.ProcessHotelChangeEvent(
                existing_booking=json.dumps(hotel_booking.content),
                change_details=json.dumps(status),
                current_date=get_current_date_string(),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

            logger.info(f"[IN_TRIP] Sending hotel change notification to user: {user.email}.")

            notification_event = {
                "summary": results.change_summary,
                "description": results.agent_response,
            }
            await send_event_notification(notification_event, user.email)

            msg = AIMessage(content=results.agent_response)
            msg.additional_kwargs = {
                "agent_classification": "InTrip",
                "is_in_trip_openning": True,
                "change_type": "hotel",
            }
            await self.persist_messages([msg])

        except Exception as e:
            logger.error(f"[IN_TRIP] Error handling hotel change: {str(e)}")

    @staticmethod
    async def extract_event_details(resource):
        try:
            extracted_data = {}

            source_info = resource.get("sourceInfo", {})
            extracted_data["sourcePnrId"] = source_info.get("sourcePnrId")

            air_pnr = resource.get("airPnr", {})
            extracted_data["legs"] = air_pnr.get("legs", [])
            extracted_data["disruptedFlightDetails"] = air_pnr.get("disruptedFlightDetails", [])
            extracted_data["otherServiceInfos"] = air_pnr.get("otherServiceInfos", [])
            extracted_data["airPnrRemarks"] = air_pnr.get("airPnrRemarks", [])

            source_statuses = []
            for leg in extracted_data["legs"]:
                flights = leg.get("flights", [])
                for flight in flights:
                    source_status = flight.get("sourceStatus")
                    if source_status:
                        source_statuses.append(
                            {
                                "sourceStatus": source_status,
                                "flightId": flight.get("flightId"),
                                "marketing": flight.get("marketing", {}),
                            }
                        )
            extracted_data["flightSourceStatuses"] = source_statuses

            operation_summary = resource.get("operationSummary", {})
            extracted_data["pnrUpdateSummary"] = operation_summary.get("pnrUpdateSummary", {})

            extracted_data["bookingHistory"] = resource.get("bookingHistory", [])

            logger.info(f"[IN_TRIP] Successfully extracted event details: {list(extracted_data.keys())}")
            return extracted_data

        except Exception as e:
            logger.error(f"[IN_TRIP] Error extracting event details: {str(e)}")
            return {}

    @staticmethod
    async def is_in_trip_booking(existing_booking: Booking) -> bool:
        today = datetime.now(timezone.utc)

        if existing_booking.type == "flight":
            return InTripAgent._is_flight_in_trip(existing_booking, today)
        else:
            if existing_booking.start_date and existing_booking.end_date:
                start_date = existing_booking.start_date.date()
                end_date = existing_booking.end_date.date()
                return start_date <= today.date() <= end_date
            return False

    @staticmethod
    def _is_flight_in_trip(booking: Booking, current_time: datetime) -> bool:
        try:
            # Extract flight details from booking
            flight_legs = InTripAgent._extract_flight_details(booking)
            if not flight_legs:
                return False

            # Check if any flight leg is currently in trip
            for flight_leg in flight_legs:
                # Parse departure and arrival times from flight_leg
                departure_time = dateutil.parser.isoparse(flight_leg.departure_time)
                arrival_time = dateutil.parser.isoparse(flight_leg.arrival_time)

                # Apply timezone if available
                if flight_leg.departure_timezone:
                    departure_tz = ZoneInfo(flight_leg.departure_timezone)
                    if departure_time.tzinfo is None:
                        departure_time = departure_time.replace(tzinfo=departure_tz)

                if flight_leg.arrival_timezone:
                    arrival_tz = ZoneInfo(flight_leg.arrival_timezone)
                    if arrival_time.tzinfo is None:
                        arrival_time = arrival_time.replace(tzinfo=arrival_tz)

                # Convert current_time to departure timezone for comparison
                if departure_time.tzinfo and current_time.tzinfo:
                    current_time_in_departure_tz = current_time.astimezone(departure_time.tzinfo)
                else:
                    current_time_in_departure_tz = current_time

                departure_minus_72h = departure_time - timedelta(hours=72)
                is_in_trip = departure_minus_72h <= current_time_in_departure_tz <= arrival_time

                if is_in_trip:
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking flight in-trip status for booking {booking.id}: {str(e)}")
            return False

    @staticmethod
    def _extract_flight_details(booking: Booking) -> list[InTripFlightInfo]:
        try:
            content = booking.content
            all_legs_info = []
            if "legs" in content:
                legs = content.get("legs", [])
                for leg_index, leg in enumerate(legs):
                    flight_segments = leg.get("flight_segments", [])
                    for segment in flight_segments:
                        flight_stops = segment.get("flight_stops", [])
                        if flight_stops:
                            first_stop = flight_stops[0]
                            last_stop = flight_stops[-1]

                            departure_time = first_stop.get("departure", "")

                            leg_info = InTripFlightInfo(
                                leg_index=leg_index,
                                confirmation_number=first_stop.get("confirmation", ""),
                                boarding_time=None,
                                gate=None,
                                terminal=None,
                                departure_time=departure_time,
                                departure_timezone=first_stop.get("departure_timezone", ""),
                                arrival_time=last_stop.get("arrival", ""),
                                arrival_timezone=last_stop.get("arrival_timezone", ""),
                                airline=first_stop.get("airline_code", ""),
                                flight_numbers=[first_stop.get("flight_number", "")],
                                origin_name=first_stop.get("origin_name", ""),
                                origin_code=first_stop.get("origin_code", ""),
                                destination_name=last_stop.get("destination_name", ""),
                                destination_code=last_stop.get("destination_code", ""),
                                price_string="",
                                seat=first_stop.get("seat", ""),
                            )
                            all_legs_info.append(leg_info)

            elif content.get("outbound") or content.get("return"):
                if content.get("outbound"):
                    outbound = content.get("outbound", {})
                    flight_segments = outbound.get("flight_segments", [])
                    for segment in flight_segments:
                        flight_stops = segment.get("flight_stops", [])
                        if flight_stops:
                            first_stop = flight_stops[0]
                            last_stop = flight_stops[-1]

                            departure_time = first_stop.get("departure_time", "")

                            leg_info = InTripFlightInfo(
                                leg_index=0,
                                confirmation_number=first_stop.get("confirmation", ""),
                                boarding_time=None,
                                gate=None,
                                terminal=None,
                                departure_time=departure_time,
                                departure_timezone=first_stop.get("departure_timezone", ""),
                                arrival_time=last_stop.get("arrival_time", ""),
                                arrival_timezone=last_stop.get("arrival_timezone", ""),
                                airline=first_stop.get("airline_code", ""),
                                flight_numbers=[first_stop.get("flight_number", "")],
                                origin_name=first_stop.get("origin_name", ""),
                                origin_code=first_stop.get("origin", ""),
                                destination_name=last_stop.get("destination_name", ""),
                                destination_code=last_stop.get("destination", ""),
                                price_string="",
                                seat=first_stop.get("seat_number", ""),
                            )
                            all_legs_info.append(leg_info)

                if content.get("return"):
                    return_flight = content.get("return", {})
                    return_flight_segments = return_flight.get("flight_segments", [])
                    for segment in return_flight_segments:
                        flight_stops = segment.get("flight_stops", [])
                        if flight_stops:
                            first_stop = flight_stops[0]
                            last_stop = flight_stops[-1]

                            departure_time = first_stop.get("departure_time", "")

                            leg_info = InTripFlightInfo(
                                leg_index=1,
                                confirmation_number=first_stop.get("confirmation", ""),
                                departure_time=departure_time,
                                departure_timezone=first_stop.get("departure_timezone", ""),
                                arrival_time=last_stop.get("arrival_time", ""),
                                arrival_timezone=last_stop.get("arrival_timezone", ""),
                                airline=first_stop.get("airline_code", ""),
                                flight_numbers=[first_stop.get("flight_number", "")],
                                origin_name=first_stop.get("origin_name", ""),
                                origin_code=first_stop.get("origin", ""),
                                destination_name=last_stop.get("destination_name", ""),
                                destination_code=last_stop.get("destination", ""),
                                price_string="",
                                seat=first_stop.get("seat_number", ""),
                            )
                            all_legs_info.append(leg_info)

            return all_legs_info

        except Exception as e:
            logger.error(f"Error parsing flight legs: {str(e)}")
            return []

    @staticmethod
    async def parse_flight_legs_from_trip_details(trip_id: str) -> list[InTripFlightInfo]:
        """Parse all flight legs from trip details API and return as list.

        Calls get trip details API and extracts flight leg data.
        Extracts: departure time, arrival time, confirmation number, seat.

        Args:
            trip_id: Trip ID to fetch details for

        Returns:
            List of flight leg dictionaries with extracted data
        """
        try:
            from flight_agent.flights_tools import FlightSearchTools

            trip_details = await FlightSearchTools.get_trip_details_spotnana(trip_id)

            if not trip_details or not trip_details.get("pnrs"):
                logger.error(f"No trip details found for trip_id {trip_id}")
                return []

            pnr_data = trip_details.get("pnrs", [{}])[0].get("data", {})
            air_pnr = pnr_data.get("airPnr", {})
            legs = air_pnr.get("legs", [])

            traveler_infos = air_pnr.get("travelerInfos", [])
            seats_map = {}
            if traveler_infos:
                seats = traveler_infos[0].get("booking", {}).get("seats", [])
                for seat in seats:
                    leg_idx = seat.get("legIdx", 0)
                    flight_idx = seat.get("flightIdx", 0)
                    seat_number = seat.get("number", "")
                    seats_map[(leg_idx, flight_idx)] = seat_number

            all_legs_info = []

            for leg_index, leg in enumerate(legs):
                flights = leg.get("flights", [])
                if flights:
                    first_flight = flights[0]
                    last_flight = flights[-1]

                    departure_time = first_flight.get("departureDateTime", {}).get("iso8601", "")

                    seat_number = seats_map.get((leg_index, 0), "")

                    marketing = first_flight.get("marketing", {})
                    airline_code = marketing.get("airlineCode", "")
                    flight_number = marketing.get("num", "")

                    leg_info = InTripFlightInfo(
                        leg_index=leg_index + 1,
                        confirmation_number=first_flight.get("vendorConfirmationNumber", ""),
                        departure_time=departure_time,
                        departure_timezone="",
                        arrival_time=last_flight.get("arrivalDateTime", {}).get("iso8601", ""),
                        arrival_timezone="",
                        airline=airline_code,
                        flight_numbers=[flight_number],
                        origin_name="",
                        origin_code=first_flight.get("origin", ""),
                        destination_name="",
                        destination_code=last_flight.get("destination", ""),
                        price_string="",
                        seat=seat_number,
                    )
                    all_legs_info.append(leg_info)

            return all_legs_info

        except Exception as e:
            logger.error(f"Error parsing flight legs from trip details for trip_id {trip_id}: {str(e)}")
            return []

    @staticmethod
    def _format_flight_message(flight_details: dict[str, Any], departure_time: datetime) -> str:
        """Generate formatted in-trip message for flight notifications."""
        try:
            airline_code = flight_details.get("airline_code", "")
            flight_number = flight_details.get("flight_number", "")
            flight_id = f"{airline_code} {flight_number}" if airline_code and flight_number else "Flight"

            origin = flight_details.get("origin_code", "")
            destination = flight_details.get("destination_code", "")
            route = f"{origin} → {destination}" if origin and destination else ""

            departure_str = departure_time.strftime("%b %d at %H:%M")

            boarding_time = departure_time - timedelta(minutes=40)
            boarding_str = boarding_time.strftime("%H:%M")

            current_time = datetime.now(departure_time.tzinfo)
            boarding_minutes_left = (boarding_time - current_time).total_seconds() / 60

            message_parts = [
                "Hello! I see you're currently on this trip, and your next flight is scheduled to depart today:",
                "",
                "---",
                "",
                f"✈️ {route} ({flight_id})",
                "• Status: On time",
            ]

            if boarding_time > current_time:
                hours = int(boarding_minutes_left // 60)
                minutes = int(boarding_minutes_left % 60)
                if hours > 0:
                    message_parts.append(f"• Boarding: {boarding_str} (in {hours} hrs {minutes} mins)")
                else:
                    message_parts.append(f"• Boarding: {boarding_str} (in {minutes} mins)")
            else:
                message_parts.append(f"• Boarding: {boarding_str}")

            message_parts.append(f"• Departure: {departure_str}")

            if flight_details.get("arrival"):
                try:
                    arrival_time = dateutil.parser.isoparse(flight_details["arrival"])
                    arrival_str = arrival_time.strftime("%H:%M")
                    message_parts.append(f"• Arrival: {arrival_str}")
                except Exception:
                    estimated_arrival = departure_time + timedelta(hours=2)
                    message_parts.append(f"• Arrival: {estimated_arrival.strftime('%H:%M')}")
            else:
                estimated_arrival = departure_time + timedelta(hours=2)
                message_parts.append(f"• Arrival: {estimated_arrival.strftime('%H:%M')}")

            if flight_details.get("seat"):
                message_parts.append(f"• Seat: {flight_details['seat']}")

            if flight_details.get("terminal"):
                message_parts.append(f"• Terminal: Terminal {flight_details['terminal']}")

            if flight_details.get("gate"):
                message_parts.append(f"• Gate: {flight_details['gate']}")

            if flight_details.get("confirmation_number"):
                message_parts.append(f"• Confirmation Number: {flight_details['confirmation_number']}")

            message_parts.extend(["", "---", "", "Can I help you with something?"])

            return "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error generating flight in-trip message: {str(e)}")
            return "Hello! I see you're currently on this trip. Can I help you with something?"

    @staticmethod
    def _extract_hotel_details(booking: Booking) -> InTripHotelInfo | None:
        """Extract hotel details from booking.content into InTripHotelInfo object."""
        try:
            content = booking.content

            hotel_name = content.get("hotel", "")
            if not hotel_name:
                return None

            return InTripHotelInfo(
                hotel_name=hotel_name,
                check_in_date=content.get("check_in_date", ""),
                check_in_time=content.get("check_in_time", "15:00:00"),
                check_out_date=content.get("check_out_date", ""),
                check_out_time=content.get("check_out_time", "11:00:00"),
                room_type=content.get("room", {}).get("option_title", ""),
                address=content.get("mapMarker", {}).get("address", ""),
                confirmation_number=content.get("reservation_number", ""),
            )

        except Exception as e:
            logger.error(f"Error extracting hotel details for booking {booking.id}: {str(e)}")
            return None

    @staticmethod
    def _format_hotel_message(hotel_info: InTripHotelInfo) -> str:
        """Format hotel info object into the message format shown in screenshot."""
        try:
            message_parts = ["Hello! I see you're on your trip and currently staying at:"]
            message_parts.extend(["", "---", "", f"🏨 **{hotel_info.hotel_name}**"])

            if hotel_info.check_in_date and hotel_info.check_in_time:
                try:
                    check_in_datetime = dateutil.parser.isoparse(
                        f"{hotel_info.check_in_date}T{hotel_info.check_in_time}"
                    )
                    check_in_str = check_in_datetime.strftime("%b %d at %H:%M")
                    message_parts.append(f"• **Check-in**: {check_in_str}")
                except Exception:
                    message_parts.append(f"• **Check-in**: {hotel_info.check_in_date} at {hotel_info.check_in_time}")

            if hotel_info.check_out_date and hotel_info.check_out_time:
                try:
                    check_out_datetime = dateutil.parser.isoparse(
                        f"{hotel_info.check_out_date}T{hotel_info.check_out_time}"
                    )
                    check_out_str = check_out_datetime.strftime("%b %d at %H:%M")
                    message_parts.append(f"• **Check-out**: {check_out_str}")
                except Exception:
                    message_parts.append(f"• **Check-out**: {hotel_info.check_out_date} at {hotel_info.check_out_time}")

            if hotel_info.room_type:
                message_parts.append(f"• **Room**: {hotel_info.room_type}")

            if hotel_info.address:
                message_parts.append(f"• **Address**: {hotel_info.address}")

            if hotel_info.confirmation_number:
                message_parts.append(f"• **Confirmation Number**: {hotel_info.confirmation_number}")

            message_parts.extend(["", "---", "", "Can I help you with something?"])

            return "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error formatting hotel message: {str(e)}")
            return "Hello! I see you're currently on this trip. Can I help you with something?"

    @staticmethod
    async def get_in_trip_status_and_message(existing_booking: Booking) -> tuple[bool, str | None]:
        """Get both in-trip status and formatted message for frontend."""
        today = datetime.now(timezone.utc)

        if existing_booking.type == "flight":
            flight_details = InTripAgent._extract_flight_details(existing_booking)
            if flight_details:
                is_in_trip = InTripAgent._is_flight_in_trip(existing_booking, today)
                if is_in_trip:
                    try:
                        departure_time = dateutil.parser.isoparse(flight_details["departure"])
                        message = InTripAgent._format_flight_message(flight_details, departure_time)
                        return True, message
                    except Exception as e:
                        logger.error(f"Error generating flight message: {str(e)}")
                        return True, None
            return False, None
        elif existing_booking.type == "accommodations":
            hotel_info = InTripAgent._extract_hotel_details(existing_booking)
            if hotel_info:
                if existing_booking.start_date and existing_booking.end_date:
                    start_date = existing_booking.start_date.date()
                    end_date = existing_booking.end_date.date()
                    is_in_trip = start_date <= today.date() <= end_date
                    if is_in_trip:
                        message = InTripAgent._format_hotel_message(hotel_info)
                        return True, message
            return False, None
        else:
            return False, None

    async def run(self, message=None, message_type="text", extra_payload=None):
        raise NotImplementedError
